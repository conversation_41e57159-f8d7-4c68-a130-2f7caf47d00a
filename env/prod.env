STAGE=
PORT=
INTERNAL_GRPC_PORT=5001
JWT_SECRET=
JWT_REFRESH_SECRET=
ENCRYPT_PUBLIC_KEY=

# PG config
# ------------------------------------------------------------------
POSTGRES_HOST=
POSTGRES_PORT=
POSTGRES_USER=
POSTGRES_PASS=
POSTGRES_DB_NAME=

# Redis config
# ------------------------------------------------------------------
REDIS_HOST=
REDIS_PORT=
REDIS_PASS=
REDIS_DB=

# Telegram bot config
# ------------------------------------------------------------------
TELEGRAM_BOT_DOMAIN=
TELEGRAM_BOT_WEBHOOK=
TELEGRAM_BOT_AUTH_TOKEN=
TELEGRAM_LOGIN_URL=

#Telegram tranding bot config
#-------------------------------------------------------------------
TELEGRAM_BOT_SNIPER_TOKEN=

# Wallet service config
# ------------------------------------------------------------------
WALLET_SERVICE_HOST=
WALLET_SERVICE_PORT=
WALLET_SERVICE_APIKEY=

#ETH
# ------------------------------------------------------------------
INFURA_RPC_URL=
TRON_GRID_API_KEY=

#EMQX
EMQX_PROTOCOL=
EMQX_HOST=
EMQX_PORT=
EMQX_USER=
EMQX_PASS=

#XBIT LANDING PAGE
LANDING_PAGE_URL=

MEME_BASE_URL=
TRANDING_BASE_URL=

REFERRAL_ADDRESS=
WEBSTITE_ADDRESS=

TOKEN_DETAILS=

ACCESS_TOKEN_EXPIRES_IN=1d
REFRESH_TOKEN_EXPIRES_IN=30d

NATS_URL=
NATS_AUTH_TOKEN=
NATS_USER=
NATS_PASS=

JWT_OIDC=
TURNKEY_API_BASE_URL=https://api.turnkey.com
TURNKEY_API_PRIVATE_KEY=
TURNKEY_API_PUBLIC_KEY=
TURNKEY_ORGANIZATION_ID=