import { Module } from '@nestjs/common';
import { WalletAuthService } from './wallet-auth.service';
import { JwtModule } from '@nestjs/jwt';
import { appConfig } from '../../configs';
import { UsersModule } from '../users/users.module';
import { NonceStore } from './nonce.store';
import { AuthUpdate } from './auth.update';
import { CachingModule } from '../caching/caching.module';
import { TelegramAuthService } from './telegram-auth.service';
import { TelegrafModule } from 'nestjs-telegraf';
import { AuthService } from './auth.service';
import { MikroOrmModule } from '@mikro-orm/nestjs';
import { UserGoogleAuthenticator } from '../settings/entities/user-google-authenticator.entity';
import { GoogleAuthService } from './google-auth.service';
import { AppI18nModule } from 'libs/i18n/i18n.module';

@Module({
    imports: [
        JwtModule.register({
            secret: appConfig.JWT_SECRET,
            signOptions: { expiresIn: appConfig.ACCESS_TOKEN_EXPIRES_IN },
        }),
        UsersModule,
        CachingModule,
        TelegrafModule.forRootAsync({
            imports: [],
            botName: appConfig.TELEGRAM_BOT_AUTH_NAME,
            useFactory: () => ({
                token: appConfig.TELEGRAM_BOT_AUTH_TOKEN,
                launchOptions: {
                    webhook: {
                        domain: appConfig.TELEGRAM_BOT_DOMAIN,
                        path: `/api/user/webhook/telegram-bot/${appConfig.TELEGRAM_BOT_AUTH_NAME}/${appConfig.TELEGRAM_BOT_WEBHOOK}`,
                    },
                },
            }),
            inject: [],
        }),
        MikroOrmModule.forFeature([UserGoogleAuthenticator]),
        AppI18nModule,
    ],
    providers: [WalletAuthService, TelegramAuthService, NonceStore, AuthUpdate, AuthService, GoogleAuthService],
    exports: [WalletAuthService, TelegramAuthService, JwtModule, TelegrafModule, AuthService, GoogleAuthService],
})
export class AuthModule {}
