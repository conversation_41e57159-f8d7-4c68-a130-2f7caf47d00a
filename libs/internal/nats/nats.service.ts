import { Inject, Injectable } from '@nestjs/common';
import { JetStreamClient, NatsConnection } from 'nats';
import { InjectPinoLogger } from 'nestjs-pino';

@Injectable()
export class NatsService {
    private js: JetStreamClient;
    constructor(
        @Inject('NATS_CLIENT') private readonly natsClient: NatsConnection,
        @InjectPinoLogger(NatsService.name) private readonly logger: any,
    ) {
        this.js = natsClient.jetstream();
    }

    async publish<T>(subject: string, data: T) {
        // add try-catch to prevent breaking main business logic
        try {
            await this.js.publish(subject, JSON.stringify(data));
        } catch (error) {
            this.logger.error(error, `Failed to publish message to subject ${subject}:`);
        }
    }

    getClient() {
        return this.natsClient;
    }
}
