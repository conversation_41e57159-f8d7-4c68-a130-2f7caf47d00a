import { Command, CommandRunner, Option } from 'nest-commander';
import { LoggerService } from '@libs/logger';

interface BasicCommandOptions {
    string?: string;
    boolean?: boolean;
    number?: number;
}

@Command({ name: 'publish-user-address', description: 'A parameter parse' })
export class PublishUserAddressCommand extends CommandRunner {
    constructor(private readonly logService: LoggerService) {
        super();
    }

    async run(passedParam: string[], options?: BasicCommandOptions): Promise<void> {
        this.logService.log('Start PublishUserAddressCommand with options', { options });
    }
}
