/* eslint-disable prettier/prettier */
import { Collection, EntityManager } from '@mikro-orm/core';
import { Injectable } from '@nestjs/common';
import { User } from '../users/entities/user.entity';
import { UserSettingsDTO } from './dto/user-settings.dto';
import { UserNotificationPreference } from './entities/user-notification-preference.entity';
import { UserNotificationPreferenceDTO } from './dto/user-notification-preference.dto';
import { UserWithdrawalWhitelistAddress } from './entities/user-withdrawal-whitelist-address.entity';
import { UserWithdrawalWhitelistAddressDTO } from './dto/user-withdrawal-whitelist-address.dto';
import { SetupNew2FAResponse, TwoFactorDTO } from './dto/user-2fa.dto';
import { UserGoogleAuthenticator } from './entities/user-google-authenticator.entity';
import { NotificationType, NotificationTypeCategoryCode } from './entities/notification-type.entity';
import { UpdatePreferenceInput } from './dto/user-notification-preference.dto';
import * as speakeasy from 'speakeasy';
import * as crypto from 'crypto';
import { INVALID_OTP_CODE, TWO_FACTOR_IS_ENABLE, TWO_FACTOR_IS_NOT_ENABLE, TWO_FACTOR_IS_NOT_SETUP } from 'libs/common/api-errors/errors';
import { ApiError } from 'libs/common/api-errors';

@Injectable()
export class SettingsService {
    constructor(private readonly em: EntityManager) {}

    async getUserSettings(userId: string, selectedFields: string[]): Promise<UserSettingsDTO> {
        const populatedFields: string[] = [];
        if (selectedFields.includes('notificationPreferences')) {
            populatedFields.push('userNotificationPreferences.notificationType');
        }
        if (selectedFields.includes('withdrawalWhitelistAddresses')) {
            populatedFields.push('userWithdrawalWhitelistAddresses');
        }
        if (selectedFields.includes('googleAuthenticator')) {
            populatedFields.push('userGoogleAuthenticator');
        }

        let user = await this.em.findOne(
            User,
            { id: userId },
            {
                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                populate: populatedFields as any,
            },
        );

        if (!user) {
            throw new Error('User not found');
        }

        // FIXME: Should be create for new user only
        if (!user.userNotificationPreferences.isInitialized()) {
            await this.createDefaultSettings(this.em, user);
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            user = await this.em.findOne(User, { id: userId }, { populate: populatedFields as any });
            if (!user) {
                throw new Error('User not found');
            }
        }

        return {
            id: user.id,
            notificationPreferences: convertCollectionToUserNotificationPreferenceDTO(
                user.userNotificationPreferences,
                user,
            ),
            withdrawalWhitelistAddresses: convertCollectionToUserWithdrawalWhitelistAddressDTO(
                user.userWithdrawalWhitelistAddresses,
                user,
            ),
            googleAuthenticator: convertUserGoogleAuthenticatorToDTO(user, user.userGoogleAuthenticator),
        };
    }

    async updatePreference(userId: string, input: UpdatePreferenceInput): Promise<boolean> {
        const user = await this.em.findOneOrFail(User, { id: userId });
        const notificationType = await this.em.findOneOrFail(NotificationType, {
            categoryCode: input.notificationTypeCode,
        });
        await this.em.nativeUpdate(
            UserNotificationPreference,
            {
                user: user,
                notificationType: notificationType,
            },
            {
                isEnabled: input.isEnabled,
            },
        );

        return true;
    }

    async setupNew2FA(userId: string): Promise<SetupNew2FAResponse> {
        const user = await this.em.findOneOrFail(User, { id: userId }, { populate: ['userGoogleAuthenticator'] });
        if (user.userGoogleAuthenticator) {
            if (user.userGoogleAuthenticator.isEnabled) {
                throw new ApiError(TWO_FACTOR_IS_ENABLE);
            }
            await this.em.removeAndFlush(user.userGoogleAuthenticator);
        }
        const secretCode = speakeasy.generateSecret({
            name: 'Xbit',
            issuer: 'Xbit',
            length: 20,
        });

        const newUserGoogleAuthenticator = new UserGoogleAuthenticator();
        newUserGoogleAuthenticator.user = user;
        newUserGoogleAuthenticator.secretKey = secretCode.base32;
        const recoveryCodes = this.generateRecoveryCodes();
        newUserGoogleAuthenticator.recoveryCodesHashed = this.hashRecoveryCodes(recoveryCodes);

        await this.em.persistAndFlush(newUserGoogleAuthenticator);
        return {
            secretCode: secretCode.base32,
            recoveryCodes: recoveryCodes,
            uri: secretCode.otpauth_url || '',
        };
    }

    async verify2FA(userId: string, code: string): Promise<boolean> {
        const auth = await this.em.findOne(UserGoogleAuthenticator, { user: { id: userId } });
        if (!auth) {
            throw new ApiError(TWO_FACTOR_IS_NOT_SETUP)
        }
        if (auth.isEnabled) {
            throw new ApiError(TWO_FACTOR_IS_ENABLE);
        }

        const isValid = speakeasy.totp.verify({
            secret: auth.secretKey,
            token: code,
            window: 1,
            encoding: 'base32',
        });

        if (!isValid) {
            return false;
        }

        auth.isEnabled = true;
        await this.em.persistAndFlush(auth);
        return true;
    }

    async disable2FA(userId: string, otpCode: string): Promise<boolean> {
        const auth = await this.em.findOne(UserGoogleAuthenticator, { user: { id: userId } });
        if (!auth) {
            throw new ApiError(TWO_FACTOR_IS_NOT_SETUP)
        }
        if (!auth.isEnabled) {
            throw new ApiError(TWO_FACTOR_IS_NOT_ENABLE);
        }

        const isValid = speakeasy.totp.verify({
            secret: auth.secretKey,
            token: otpCode,
            window: 1,
            encoding: 'base32',
        });

        if (!isValid) {
            throw new ApiError(INVALID_OTP_CODE);
        }

        await this.em.removeAndFlush(auth);
        return true;
    }

    // FIXME: Should be create for new user only
    // Use upsert to keep backward compatibility
    async createDefaultSettings(txEm: EntityManager, user: User): Promise<void> {
        const smartMoneyActivity = await txEm.upsert(NotificationType, {
            categoryCode: NotificationTypeCategoryCode.SmartMoneyActivity,
            categoryName: 'Smart Money Activity',
            description: 'Receive notifications when your smart money activity changes',
        });
        const priceChange = await txEm.upsert(NotificationType, {
            categoryCode: NotificationTypeCategoryCode.PriceChange,
            categoryName: 'Price Change',
            description: 'Receive notifications when the price of your assets changes',
        });
        const futuresSignal = await txEm.upsert(NotificationType, {
            categoryCode: NotificationTypeCategoryCode.FuturesSignal,
            categoryName: 'Futures Signal',
            description: 'Receive notifications when getting futures signal',
        });
        const others = await txEm.upsert(NotificationType, {
            categoryCode: NotificationTypeCategoryCode.Others,
            categoryName: 'Others',
            description: 'All other notifications',
        });

        // Smart Money Activity
        await this.createIfNotExists(txEm, user, smartMoneyActivity, 'FCM', true);

        // Price Change
        await this.createIfNotExists(txEm, user, priceChange, 'FCM', false);

        // Futures Signal
        await this.createIfNotExists(txEm, user, futuresSignal, 'FCM', false);

        // Others
        await this.createIfNotExists(txEm, user, others, 'FCM', true);
    }

    async createIfNotExists(txEm: EntityManager, user: User, notificationType: NotificationType, channel: string, isEnabled: boolean): Promise<UserNotificationPreference> {
        const now = new Date();
        let existingPref = await txEm.findOne(UserNotificationPreference, {
            user: user,
            notificationType: notificationType,
            channel: channel,
        });
        if (!existingPref) {
            existingPref = txEm.create(UserNotificationPreference, {
                user: user,
                notificationType: notificationType,
                channel: channel,
                isEnabled: isEnabled,
                createdAt: now,
                updatedAt: now,
            });
            txEm.persist(existingPref);
        }
        return existingPref;
    }   

    private generateRecoveryCodes(count = 10): string[] {
        return Array.from({ length: count }, () =>
          Math.random().toString(36).slice(-10).toUpperCase()
        );
    }

    private hashRecoveryCodes(codes: string[]): string {
        const combined = codes.join(',');
        return crypto.createHash('sha256').update(combined).digest('hex');
    }
}

function convertCollectionToUserNotificationPreferenceDTO(
    collection: Collection<UserNotificationPreference, object>,
    user: User,
): UserNotificationPreferenceDTO[] {
    return collection.toArray().map((item) => ({
        id: item.id,
        userId: user.id,
        notificationTypeCode: item.notificationType.categoryCode,
        channel: item.channel,
        isEnabled: item.notificationType.categoryCode === NotificationTypeCategoryCode.SmartMoneyActivity.toString() || item.notificationType.categoryCode === NotificationTypeCategoryCode.Others.toString() ? item.isEnabled : false,
    }));
}

function convertCollectionToUserWithdrawalWhitelistAddressDTO(
    collection: Collection<UserWithdrawalWhitelistAddress, object>,
    user: User,
): UserWithdrawalWhitelistAddressDTO[] {
    return collection.toArray().map((item) => ({
        id: item.id,
        userId: user.id,
        address: item.address,
        nickname: item.nickname,
    }));
}

function convertUserGoogleAuthenticatorToDTO(
    user: User,
    userGoogleAuthenticator?: UserGoogleAuthenticator,
): TwoFactorDTO | undefined {
    if (!userGoogleAuthenticator) {
        return undefined;
    }
    return {
        userId: user.id,
        isEnabled: userGoogleAuthenticator.isEnabled,
    };
}
