import { EntityManager, EntityRepository } from '@mikro-orm/core';
import { User } from '../entities/user.entity';
import { UserEmbeddedWallet } from '../entities/user-embedded-wallet.entity';
import { EmbeddedWalletDto } from '../dto/embedded-wallet.dto';
import { ApiError } from 'libs/common/api-errors';
import { WALLET_NOT_FOUND, WALLET_ACCESS_DENIED, WALLET_NAME_ALREADY_EXISTS } from 'libs/common/api-errors/errors';

export class UserEmbeddedWalletRepository extends EntityRepository<UserEmbeddedWallet> {
    async createWalletsForUser(
        em: EntityManager,
        user: User,
        wallets: EmbeddedWalletDto[],
    ): Promise<UserEmbeddedWallet[]> {
        // const em = this.em.fork();
        // Get existing wallets for the user to check for name conflicts
        const existingWallets = await em.find(UserEmbeddedWallet, { user: user.id });

        // Check for duplicate wallet names within the new wallets being created (same name + same chain)
        const newWalletNameChainPairs = wallets
            .map((w) => ({
                name: w.name?.trim().toLowerCase(),
                chain: w.chain,
            }))
            .filter((pair) => pair.name);

        const duplicateNameChainPairs = newWalletNameChainPairs.filter(
            (pair, index) =>
                newWalletNameChainPairs.findIndex((p) => p.name === pair.name && p.chain === pair.chain) !== index,
        );

        if (duplicateNameChainPairs.length > 0) {
            throw new ApiError(WALLET_NAME_ALREADY_EXISTS);
        }

        // Check for conflicts with existing wallets (same name + same chain)
        for (const wallet of wallets) {
            if (wallet.name?.trim()) {
                const existingWalletWithSameNameAndChain = existingWallets.find(
                    (existing) =>
                        existing.name?.trim().toLowerCase() === wallet.name.trim().toLowerCase() &&
                        existing.chain === wallet.chain,
                );

                if (existingWalletWithSameNameAndChain) {
                    throw new ApiError(WALLET_NAME_ALREADY_EXISTS);
                }
            }
        }

        const result: UserEmbeddedWallet[] = [];
        for (const wallet of wallets) {
            const w = new UserEmbeddedWallet();
            w.user = user;
            w.chain = wallet.chain;
            w.walletAddress = wallet.walletAddress;
            w.walletAccountId = wallet.walletAccountId;
            w.walletId = wallet.walletId;
            w.hdPath = wallet.hdPath;
            w.name = wallet.name;
            await em.persistAndFlush(w);
            result.push(w);
        }
        return result;
    }

    async updateWalletName(
        wallet: UserEmbeddedWallet,
        em: EntityManager,
        user: User,
        newName: string,
    ): Promise<UserEmbeddedWallet> {
        if (!wallet) {
            throw new ApiError(WALLET_NOT_FOUND);
        }

        // Additional security check to ensure the wallet belongs to the user
        if (wallet.user.id !== user.id) {
            throw new ApiError(WALLET_ACCESS_DENIED);
        }

        wallet.name = newName;
        await em.persistAndFlush(wallet);

        return wallet;
    }
}
